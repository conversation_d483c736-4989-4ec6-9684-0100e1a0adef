{"name": "policy-gap-analyzer", "version": "1.0.0", "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview"}, "dependencies": {"@json2csv/plainjs": "^7.0.6", "@supabase/supabase-js": "^2.45.4", "@tailwindcss/typography": "^0.5.16", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "pdfjs-dist": "^5.3.93", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.7.0"}, "devDependencies": {"@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.10", "vite": "^5.4.8"}}
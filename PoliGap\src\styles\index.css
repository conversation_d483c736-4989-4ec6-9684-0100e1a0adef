@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Space+Grotesk:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  * {
    @apply antialiased;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, sans-serif;
  }
}

/* Custom utility classes */
@layer utilities {
  /* Glass morphism effects */
  .glass {
    @apply bg-white/10 backdrop-blur-lg;
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  .glass-dark {
    @apply bg-gray-900/10 backdrop-blur-lg;
    border: 1px solid rgba(31, 41, 55, 0.18);
  }

  /* Modern shadows */
  .shadow-soft {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.15);
  }

  .shadow-glow-blue {
    box-shadow: 0 0 20px rgba(34, 211, 238, 0.15);
  }

  /* AI Feature specific animations */
  .ai-pulse {
    animation: ai-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .dna-rotate {
    animation: dna-rotate 3s linear infinite;
  }

  .cross-pollinate {
    animation: cross-pollinate 1.5s ease-in-out infinite alternate;
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .bg-gradient-accent {
    background: linear-gradient(135deg, #00B7EB 0%, #667eea 100%);
  }

  .bg-gradient-surface {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }

  /* Text gradients */
  .text-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-accent {
    background: linear-gradient(135deg, #00B7EB 0%, #9333ea 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Hide scrollbar for webkit browsers */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Button hover effects */
  .btn-hover {
    @apply transition-all duration-200 ease-out;
  }

  .btn-hover:hover {
    @apply transform -translate-y-1 shadow-glow;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 ease-out;
  }

  .card-hover:hover {
    @apply transform -translate-y-2 shadow-soft;
  }

  /* Custom animations */
  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    0% {
      opacity: 0;
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-slideInLeft {
    animation: slideInLeft 0.6s ease-out;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  /* AI elements */
  .ai-glow {
    background: linear-gradient(45deg, #00B7EB, #9333ea);
    box-shadow: 0 0 20px rgba(0, 183, 235, 0.3);
  }

  .ai-pulse {
    animation: ai-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes ai-pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* Cyberpunk glitch effects */
  .glitch-hover:hover {
    animation: glitch 0.3s ease-in-out;
  }

  @keyframes glitch {
    0% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
    100% { transform: translate(0); }
  }

  /* Enhanced neon glow effects */
  .shadow-glow-cyan {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3), 0 0 40px rgba(6, 182, 212, 0.1);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3), 0 0 40px rgba(147, 51, 234, 0.1);
  }

  /* Cyberpunk text effects */
  .text-neon {
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }

  /* Responsive design utilities */
  @media (max-width: 768px) {
    .mobile-stack {
      flex-direction: column;
    }

    .mobile-full {
      width: 100%;
    }
  }

  /* Wave animations */
  @keyframes wave-float {
    0%, 100% {
      transform: translateY(0) scaleX(1);
    }
    50% {
      transform: translateY(-20px) scaleX(1.05);
    }
  }

  @keyframes wave-float-delayed {
    0%, 100% {
      transform: translateY(0) scaleX(1);
    }
    50% {
      transform: translateY(-15px) scaleX(1.03);
    }
  }

  @keyframes wave-float-slow {
    0%, 100% {
      transform: translateY(0) scaleX(1);
    }
    50% {
      transform: translateY(-10px) scaleX(1.02);
    }
  }

  @keyframes wave-motion {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-wave {
    animation: wave-float 3s ease-in-out infinite;
    transform-origin: center bottom;
  }

  .animate-wave-delayed {
    animation: wave-float-delayed 4s ease-in-out infinite;
    animation-delay: -1s;
    transform-origin: center bottom;
  }

  .animate-wave-slow {
    animation: wave-float-slow 5s ease-in-out infinite;
    animation-delay: -2s;
    transform-origin: center bottom;
  }

  .animate-bg-wave {
    animation: wave-motion 8s ease infinite;
    background-size: 200% 200%;
  }
}

/* AI feature specific animations */
@keyframes ai-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes dna-rotate {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.05);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

@keyframes cross-pollinate {
  0% {
    transform: translateX(0) rotate(0deg);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  100% {
    transform: translateX(2px) rotate(1deg);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
}

/* Wave animations for landing page */
@keyframes wave {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes wave-reverse {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-wave {
  animation: wave 8s ease-in-out infinite;
}

.animate-wave-reverse {
  animation: wave-reverse 10s ease-in-out infinite;
}

/* Custom Chat Markdown Styling */
.prose h1, .prose h2, .prose h3 {
  @apply font-semibold text-gray-800;
}

.prose h1 {
  @apply text-lg mb-3 mt-2;
}

.prose h2 {
  @apply text-base mb-2 mt-3;
}

.prose h3 {
  @apply text-sm mb-2 mt-2;
}

.prose p {
  @apply text-gray-700 mb-2 leading-relaxed text-sm;
}

.prose ul {
  @apply space-y-1 my-2;
}

.prose li {
  @apply text-gray-700 text-sm flex items-start;
}

.prose strong {
  @apply font-semibold text-gray-900;
}

.prose em {
  @apply italic text-gray-600;
}

.prose code {
  @apply bg-gray-100 text-gray-800 px-1.5 py-0.5 rounded text-xs font-mono;
}

.prose blockquote {
  @apply border-l-4 border-blue-200 pl-3 py-1 bg-blue-50 my-2 rounded-r;
}

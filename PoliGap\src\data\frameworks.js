// Consolidated Compliance Frameworks Data
// This file contains comprehensive framework information used across all components

export const frameworksData = [
  {
    id: 'gdpr',
    name: 'GDPR',
    fullName: 'General Data Protection Regulation',
    description: 'The EU regulation on data protection and privacy that applies to all individuals within the EU and EEA. It addresses the export of personal data outside the EU and EEA areas.',
    category: 'privacy',
    regions: ['eu', 'global'],
    region: 'EU',
    countries: ['Germany', 'France', 'Italy', 'Spain', 'Netherlands', 'All EU Countries'],
    icon: '🇪🇺',
    color: 'blue',
    yearIntroduced: 2018,
    maxFine: '€20M or 4% of global revenue',
    implementationTime: '3-6 months',
    complexity: 'High',
    keyDomains: ['data-protection', 'privacy-rights', 'consent-management', 'data-security'],
    keyRequirements: [
      'Data Protection Impact Assessments',
      'Consent Management',
      'Right to Erasure',
      'Data Breach Notifications',
      'Privacy by Design'
    ],
    synergies: ['iso27001', 'hipaa', 'ccpa'],
    applicableIndustries: ['Technology', 'Finance', 'Healthcare', 'Retail', 'All'],
    fields: ['Data Protection', 'Privacy', 'Digital Rights'],
    penaltyRange: '€20M or 4% annual turnover',
    lastUpdated: '2018-05-25'
  },
  {
    id: 'iso27001',
    name: 'ISO 27001',
    fullName: 'Information Security Management Systems',
    description: 'International standard for information security management systems (ISMS). It provides a systematic approach to managing sensitive company information.',
    category: 'security',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide'],
    icon: '🔐',
    color: 'purple',
    yearIntroduced: 2005,
    maxFine: 'Varies by jurisdiction',
    implementationTime: '6-12 months',
    complexity: 'High',
    keyDomains: ['information-security', 'risk-management', 'access-control', 'incident-management'],
    keyRequirements: [
      'ISMS Implementation',
      'Risk Assessment',
      'Security Controls',
      'Continuous Monitoring',
      'Internal Audits'
    ],
    synergies: ['gdpr', 'sox', 'nist'],
    applicableIndustries: ['Technology', 'Finance', 'Healthcare', 'Manufacturing', 'Government'],
    fields: ['Information Security', 'Risk Management', 'Cybersecurity'],
    penaltyRange: 'Certification loss, reputation damage',
    lastUpdated: '2022-10-15'
  },
  {
    id: 'hipaa',
    name: 'HIPAA',
    fullName: 'Health Insurance Portability and Accountability Act',
    description: 'US healthcare privacy and security regulations for protected health information (PHI) and electronic PHI (ePHI).',
    category: 'healthcare',
    regions: ['us'],
    region: 'US',
    countries: ['United States'],
    icon: '🏥',
    color: 'green',
    yearIntroduced: 1996,
    maxFine: '$1.5M per incident',
    implementationTime: '4-8 months',
    complexity: 'High',
    keyDomains: ['healthcare-privacy', 'data-security', 'access-control', 'audit-trails'],
    keyRequirements: [
      'Administrative Safeguards',
      'Physical Safeguards',
      'Technical Safeguards',
      'Business Associate Agreements',
      'Risk Assessments'
    ],
    synergies: ['gdpr', 'iso27001', 'hitech'],
    applicableIndustries: ['Healthcare', 'Insurance', 'Technology (Healthcare)'],
    fields: ['Healthcare', 'Medical Privacy', 'Health Data Protection'],
    penaltyRange: '$127 - $1.92M per violation',
    lastUpdated: '2013-09-23'
  },
  {
    id: 'sox',
    name: 'SOX',
    fullName: 'Sarbanes-Oxley Act',
    description: 'US federal law that mandates certain practices in financial record keeping and reporting for corporations.',
    category: 'financial',
    regions: ['us'],
    region: 'US',
    countries: ['United States'],
    icon: '📊',
    color: 'orange',
    yearIntroduced: 2002,
    maxFine: '$5M and 20 years imprisonment',
    implementationTime: '6-12 months',
    complexity: 'High',
    keyDomains: ['financial-reporting', 'internal-controls', 'audit-trails', 'data-integrity'],
    keyRequirements: [
      'Internal Controls',
      'Financial Reporting',
      'Audit Committee Requirements',
      'Management Assessment',
      'External Auditor Attestation'
    ],
    synergies: ['iso27001', 'cobit', 'coso'],
    applicableIndustries: ['Public Companies', 'Finance', 'Accounting'],
    fields: ['Financial Reporting', 'Corporate Governance', 'Internal Controls'],
    penaltyRange: 'Up to $5M fine and 20 years prison',
    lastUpdated: '2002-07-30'
  },
  {
    id: 'ccpa',
    name: 'CCPA',
    fullName: 'California Consumer Privacy Act',
    description: 'California state law intended to enhance privacy rights and consumer protection for residents of California.',
    category: 'privacy',
    regions: ['us'],
    region: 'California, US',
    countries: ['United States (California)'],
    icon: '🏖️',
    color: 'blue',
    yearIntroduced: 2020,
    maxFine: '$7,500 per violation',
    implementationTime: '3-6 months',
    complexity: 'Medium',
    keyDomains: ['consumer-privacy', 'data-rights', 'transparency', 'opt-out'],
    keyRequirements: [
      'Consumer Right to Know',
      'Right to Delete',
      'Right to Opt-Out',
      'Non-Discrimination',
      'Privacy Policy Requirements'
    ],
    synergies: ['gdpr', 'cpra', 'cdpa'],
    applicableIndustries: ['Technology', 'Retail', 'Marketing', 'E-commerce'],
    fields: ['Consumer Privacy', 'Data Rights', 'Digital Marketing'],
    penaltyRange: '$2,500 - $7,500 per violation',
    lastUpdated: '2020-01-01'
  },
  {
    id: 'pci-dss',
    name: 'PCI DSS',
    fullName: 'Payment Card Industry Data Security Standard',
    description: 'Information security standard for organizations that handle branded credit cards from major card schemes.',
    category: 'financial',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide'],
    icon: '💳',
    color: 'red',
    yearIntroduced: 2004,
    maxFine: '$100,000 per month',
    implementationTime: '4-8 months',
    complexity: 'High',
    keyDomains: ['payment-security', 'network-security', 'data-encryption', 'access-control'],
    keyRequirements: [
      'Secure Network Architecture',
      'Protect Cardholder Data',
      'Vulnerability Management',
      'Access Control Measures',
      'Network Monitoring'
    ],
    synergies: ['iso27001', 'sox', 'gdpr'],
    applicableIndustries: ['Retail', 'E-commerce', 'Financial Services', 'Hospitality'],
    fields: ['Payment Security', 'Financial Data Protection', 'Card Processing'],
    penaltyRange: '$5,000 - $100,000 per month',
    lastUpdated: '2022-03-31'
  },
  {
    id: 'iso-9001',
    name: 'ISO 9001',
    fullName: 'Quality Management Systems',
    description: 'International standard that specifies requirements for a quality management system.',
    category: 'quality',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide'],
    icon: '⭐',
    color: 'green',
    yearIntroduced: 1987,
    maxFine: 'N/A (Certification standard)',
    implementationTime: '6-12 months',
    complexity: 'Medium',
    keyDomains: ['quality-management', 'customer-satisfaction', 'continuous-improvement', 'process-control'],
    keyRequirements: [
      'Quality Management System',
      'Leadership Commitment',
      'Risk-Based Thinking',
      'Customer Focus',
      'Continuous Improvement'
    ],
    synergies: ['iso14001', 'iso45001', 'iso27001'],
    applicableIndustries: ['Manufacturing', 'Services', 'Healthcare', 'Technology'],
    fields: ['Quality Management', 'Process Improvement', 'Customer Satisfaction'],
    penaltyRange: 'N/A (voluntary standard)',
    lastUpdated: '2015-09-15'
  },
  {
    id: 'nist-csf',
    name: 'NIST CSF',
    fullName: 'NIST Cybersecurity Framework',
    description: 'Framework for improving critical infrastructure cybersecurity consisting of standards, guidelines, and best practices.',
    category: 'security',
    regions: ['us', 'global'],
    region: 'US/Global',
    countries: ['United States', 'International adoption'],
    icon: '🛡️',
    color: 'indigo',
    yearIntroduced: 2014,
    maxFine: 'N/A (voluntary framework)',
    implementationTime: '6-18 months',
    complexity: 'Medium',
    keyDomains: ['cybersecurity', 'risk-management', 'incident-response', 'recovery'],
    keyRequirements: [
      'Identify',
      'Protect',
      'Detect',
      'Respond',
      'Recover'
    ],
    synergies: ['iso27001', 'nist-800-53', 'sox'],
    applicableIndustries: ['Critical Infrastructure', 'Finance', 'Energy', 'Technology'],
    fields: ['Cybersecurity', 'Infrastructure Protection', 'Risk Management'],
    penaltyRange: 'N/A (voluntary framework)',
    lastUpdated: '2018-04-16'
  },
  {
    id: 'coso',
    name: 'COSO',
    fullName: 'Committee of Sponsoring Organizations Framework',
    description: 'Framework for enterprise risk management, internal control, and fraud deterrence.',
    category: 'governance',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide'],
    icon: '⚙️',
    color: 'gray',
    yearIntroduced: 1992,
    maxFine: 'N/A (Best Practice Framework)',
    implementationTime: '6-18 months',
    complexity: 'High',
    keyDomains: ['internal-control', 'risk-management', 'fraud-prevention', 'governance'],
    keyRequirements: [
      'Control Environment',
      'Risk Assessment',
      'Control Activities',
      'Information & Communication',
      'Monitoring Activities'
    ],
    synergies: ['sox', 'cobit', 'iso27001'],
    applicableIndustries: ['Public Companies', 'Finance', 'Government', 'Non-profits'],
    fields: ['Internal Controls', 'Risk Management', 'Corporate Governance'],
    penaltyRange: 'N/A (voluntary framework)',
    lastUpdated: '2017-06-01'
  },
  {
    id: 'cobit',
    name: 'COBIT',
    fullName: 'Control Objectives for Information and Related Technologies',
    description: 'Framework for governance and management of enterprise IT created by ISACA.',
    category: 'governance',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide'],
    icon: '⚙️',
    color: 'gray',
    yearIntroduced: 1996,
    maxFine: 'N/A (Best Practice Framework)',
    implementationTime: '6-18 months',
    complexity: 'High',
    keyDomains: ['it-governance', 'risk-management', 'resource-management', 'performance-measurement'],
    keyRequirements: [
      'Governance Framework',
      'Management Processes',
      'Control Activities',
      'Information & Communication',
      'Monitoring Activities'
    ],
    synergies: ['sox', 'iso27001', 'coso'],
    applicableIndustries: ['All (IT-dependent organizations)'],
    fields: ['IT Governance', 'Technology Management', 'Digital Transformation'],
    penaltyRange: 'N/A (voluntary framework)',
    lastUpdated: '2019-02-28'
  },
  {
    id: 'nist-800-53',
    name: 'NIST 800-53',
    fullName: 'Security Controls for Federal Information Systems',
    description: 'Catalog of security and privacy controls for federal information systems and organizations.',
    category: 'security',
    regions: ['us'],
    region: 'US',
    countries: ['United States'],
    icon: '🏛️',
    color: 'blue',
    yearIntroduced: 2005,
    maxFine: 'Contract termination, legal action',
    implementationTime: '8-16 months',
    complexity: 'Very High',
    keyDomains: ['access-control', 'audit-accountability', 'configuration-management', 'identification-authentication'],
    keyRequirements: [
      'Access Control',
      'Audit and Accountability',
      'Configuration Management',
      'Identification and Authentication',
      'System and Communications Protection'
    ],
    synergies: ['nist-csf', 'iso27001', 'fedramp'],
    applicableIndustries: ['Government', 'Defense Contractors', 'Federal Suppliers'],
    fields: ['Federal Security', 'Government Compliance', 'Defense'],
    penaltyRange: 'Contract penalties, legal consequences',
    lastUpdated: '2020-09-23'
  },
  {
    id: 'fedramp',
    name: 'FedRAMP',
    fullName: 'Federal Risk and Authorization Management Program',
    description: 'US government program that provides standardized approach to security assessment for cloud products.',
    category: 'security',
    regions: ['us'],
    region: 'US',
    countries: ['United States'],
    icon: '☁️',
    color: 'indigo',
    yearIntroduced: 2011,
    maxFine: 'Loss of government contracts',
    implementationTime: '12-24 months',
    complexity: 'Very High',
    keyDomains: ['cloud-security', 'continuous-monitoring', 'risk-management', 'authorization'],
    keyRequirements: [
      'Security Assessment',
      'Authorization to Operate',
      'Continuous Monitoring',
      'NIST 800-53 Controls',
      'Third-Party Assessment'
    ],
    synergies: ['nist-800-53', 'iso27001', 'sox'],
    applicableIndustries: ['Cloud Service Providers', 'Government Contractors'],
    fields: ['Cloud Security', 'Government Cloud', 'Federal Authorization'],
    penaltyRange: 'Contract termination, business impact',
    lastUpdated: '2021-12-15'
  },
  {
    id: 'iso-14001',
    name: 'ISO 14001',
    fullName: 'Environmental Management Systems',
    description: 'International standard that specifies requirements for an environmental management system.',
    category: 'environmental',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide'],
    icon: '🌍',
    color: 'green',
    yearIntroduced: 1996,
    maxFine: 'N/A (Certification standard)',
    implementationTime: '6-12 months',
    complexity: 'Medium',
    keyDomains: ['environmental-management', 'sustainability', 'waste-reduction', 'compliance'],
    keyRequirements: [
      'Environmental Policy',
      'Environmental Aspects',
      'Legal Requirements',
      'Environmental Objectives',
      'Internal Audits'
    ],
    synergies: ['iso9001', 'iso45001', 'iso50001'],
    applicableIndustries: ['Manufacturing', 'Construction', 'Energy', 'Chemical'],
    fields: ['Environmental Management', 'Sustainability', 'Climate Change'],
    penaltyRange: 'N/A (voluntary standard)',
    lastUpdated: '2015-09-15'
  },
  {
    id: 'iso-45001',
    name: 'ISO 45001',
    fullName: 'Occupational Health and Safety Management Systems',
    description: 'International standard for occupational health and safety management systems.',
    category: 'safety',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide'],
    icon: '🦺',
    color: 'orange',
    yearIntroduced: 2018,
    maxFine: 'N/A (Certification standard)',
    implementationTime: '6-12 months',
    complexity: 'Medium',
    keyDomains: ['occupational-health', 'safety-management', 'risk-control', 'worker-participation'],
    keyRequirements: [
      'OH&S Policy',
      'Hazard Identification',
      'Risk Assessment',
      'Worker Participation',
      'Incident Investigation'
    ],
    synergies: ['iso9001', 'iso14001', 'iso27001'],
    applicableIndustries: ['Manufacturing', 'Construction', 'Mining', 'Oil & Gas'],
    fields: ['Occupational Health', 'Workplace Safety', 'Worker Protection'],
    penaltyRange: 'N/A (voluntary standard)',
    lastUpdated: '2018-03-12'
  },
  {
    id: 'ffiec',
    name: 'FFIEC',
    fullName: 'Federal Financial Institutions Examination Council',
    description: 'US federal guidelines for financial institutions regarding IT examination procedures.',
    category: 'financial',
    regions: ['us'],
    region: 'US',
    countries: ['United States'],
    icon: '🏦',
    color: 'blue',
    yearIntroduced: 1979,
    maxFine: 'Regulatory sanctions, fines',
    implementationTime: '6-12 months',
    complexity: 'High',
    keyDomains: ['financial-it', 'cybersecurity', 'risk-management', 'audit'],
    keyRequirements: [
      'IT Risk Assessment',
      'Information Security',
      'Business Continuity',
      'Vendor Management',
      'Audit and Monitoring'
    ],
    synergies: ['sox', 'pci-dss', 'nist-csf'],
    applicableIndustries: ['Banks', 'Credit Unions', 'Financial Services'],
    fields: ['Financial IT', 'Banking Security', 'Financial Risk'],
    penaltyRange: 'Varies (regulatory sanctions)',
    lastUpdated: '2021-10-15'
  },
  {
    id: 'cis-controls',
    name: 'CIS Controls',
    fullName: 'Center for Internet Security Critical Security Controls',
    description: 'Prioritized set of actions that collectively form a defense-in-depth set of best practices.',
    category: 'security',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide'],
    icon: '🔧',
    color: 'teal',
    yearIntroduced: 2008,
    maxFine: 'N/A (Best practice framework)',
    implementationTime: '3-12 months',
    complexity: 'Medium',
    keyDomains: ['cybersecurity', 'asset-management', 'access-control', 'incident-response'],
    keyRequirements: [
      'Inventory of Assets',
      'Inventory of Software',
      'Data Protection',
      'Secure Configuration',
      'Account Management'
    ],
    synergies: ['nist-csf', 'iso27001', 'sans'],
    applicableIndustries: ['All (IT-dependent organizations)'],
    fields: ['Cybersecurity', 'IT Security', 'Threat Protection'],
    penaltyRange: 'N/A (voluntary framework)',
    lastUpdated: '2021-05-18'
  },
  {
    id: 'gdpr-uk',
    name: 'UK GDPR',
    fullName: 'UK General Data Protection Regulation',
    description: 'UK version of GDPR that applies after Brexit, with similar but adapted requirements.',
    category: 'privacy',
    regions: ['uk'],
    region: 'UK',
    countries: ['United Kingdom'],
    icon: '🇬🇧',
    color: 'blue',
    yearIntroduced: 2021,
    maxFine: '£17.5M or 4% of global revenue',
    implementationTime: '3-6 months',
    complexity: 'High',
    keyDomains: ['data-protection', 'privacy-rights', 'consent-management', 'data-security'],
    keyRequirements: [
      'Lawful Basis for Processing',
      'Data Subject Rights',
      'Data Protection Impact Assessments',
      'Breach Notification',
      'Privacy by Design'
    ],
    synergies: ['gdpr', 'dpa2018', 'pecr'],
    applicableIndustries: ['Technology', 'Finance', 'Healthcare', 'Retail', 'All'],
    fields: ['Data Protection', 'Privacy', 'Digital Rights'],
    penaltyRange: '£17.5M or 4% annual turnover',
    lastUpdated: '2021-01-01'
  },
  {
    id: 'pipeda',
    name: 'PIPEDA',
    fullName: 'Personal Information Protection and Electronic Documents Act',
    description: 'Canadian federal privacy law for private sector organizations.',
    category: 'privacy',
    regions: ['canada'],
    region: 'Canada',
    countries: ['Canada'],
    icon: '🇨🇦',
    color: 'red',
    yearIntroduced: 2000,
    maxFine: 'Up to $100,000',
    implementationTime: '3-6 months',
    complexity: 'Medium',
    keyDomains: ['privacy-protection', 'consent', 'data-security', 'transparency'],
    keyRequirements: [
      'Consent for Collection',
      'Limited Collection',
      'Limited Use and Disclosure',
      'Accuracy',
      'Safeguards'
    ],
    synergies: ['gdpr', 'ccpa', 'cpra'],
    applicableIndustries: ['Technology', 'Finance', 'Healthcare', 'Retail'],
    fields: ['Privacy Protection', 'Personal Data', 'Consumer Rights'],
    penaltyRange: 'Up to $100,000 CAD',
    lastUpdated: '2018-11-01'
  },
  {
    id: 'lgpd',
    name: 'LGPD',
    fullName: 'Lei Geral de Proteção de Dados',
    description: 'Brazilian general data protection law that regulates the processing of personal data.',
    category: 'privacy',
    regions: ['brazil'],
    region: 'Brazil',
    countries: ['Brazil'],
    icon: '🇧🇷',
    color: 'green',
    yearIntroduced: 2020,
    maxFine: 'R$50M or 2% of revenue',
    implementationTime: '3-6 months',
    complexity: 'High',
    keyDomains: ['data-protection', 'privacy-rights', 'consent', 'data-security'],
    keyRequirements: [
      'Lawful Basis',
      'Data Subject Rights',
      'Privacy by Design',
      'Data Protection Officer',
      'Impact Assessment'
    ],
    synergies: ['gdpr', 'ccpa', 'pipeda'],
    applicableIndustries: ['Technology', 'Finance', 'Healthcare', 'E-commerce'],
    fields: ['Data Protection', 'Privacy Rights', 'Personal Data'],
    penaltyRange: 'R$50M or 2% of revenue',
    lastUpdated: '2020-09-18'
  },
  {
    id: 'appi',
    name: 'APPI',
    fullName: 'Act on Protection of Personal Information',
    description: 'Japanese privacy law that regulates the handling of personal information.',
    category: 'privacy',
    regions: ['japan'],
    region: 'Japan',
    countries: ['Japan'],
    icon: '🇯🇵',
    color: 'red',
    yearIntroduced: 2003,
    maxFine: '¥10M or imprisonment',
    implementationTime: '4-8 months',
    complexity: 'Medium',
    keyDomains: ['personal-information', 'consent', 'data-security', 'cross-border-transfer'],
    keyRequirements: [
      'Consent for Use',
      'Purpose Specification',
      'Data Minimization',
      'Security Measures',
      'Transfer Restrictions'
    ],
    synergies: ['gdpr', 'apec-cbpr', 'ccpa'],
    applicableIndustries: ['Technology', 'Finance', 'Healthcare', 'Retail'],
    fields: ['Personal Information', 'Privacy Protection', 'Data Security'],
    penaltyRange: '¥10M fine or up to 1 year imprisonment',
    lastUpdated: '2022-04-01'
  },
  {
    id: 'pdpa-singapore',
    name: 'PDPA (Singapore)',
    fullName: 'Personal Data Protection Act (Singapore)',
    description: 'Singapore law that governs the collection, use, and disclosure of personal data.',
    category: 'privacy',
    regions: ['singapore'],
    region: 'Singapore',
    countries: ['Singapore'],
    icon: '🇸🇬',
    color: 'teal',
    yearIntroduced: 2012,
    maxFine: 'S$1M',
    implementationTime: '3-6 months',
    complexity: 'Medium',
    keyDomains: ['personal-data', 'consent', 'notification', 'data-security'],
    keyRequirements: [
      'Consent Obligation',
      'Purpose Limitation',
      'Notification Obligation',
      'Access and Correction',
      'Data Protection Provisions'
    ],
    synergies: ['gdpr', 'appi', 'pipeda'],
    applicableIndustries: ['Technology', 'Finance', 'Healthcare', 'Telecommunications'],
    fields: ['Personal Data Protection', 'Privacy', 'Data Governance'],
    penaltyRange: 'Up to S$1M SGD',
    lastUpdated: '2021-02-01'
  },
  {
    id: 'dpa-uae',
    name: 'UAE DPA',
    fullName: 'UAE Data Protection Law',
    description: 'Federal data protection law for the United Arab Emirates.',
    category: 'privacy',
    regions: ['uae'],
    region: 'UAE',
    countries: ['United Arab Emirates'],
    icon: '🇦🇪',
    color: 'orange',
    yearIntroduced: 2022,
    maxFine: 'AED 10M',
    implementationTime: '6-12 months',
    complexity: 'Medium',
    keyDomains: ['data-protection', 'consent', 'cross-border-transfer', 'data-security'],
    keyRequirements: [
      'Lawful Processing',
      'Data Subject Rights',
      'Data Protection Officer',
      'Impact Assessment',
      'Breach Notification'
    ],
    synergies: ['gdpr', 'ccpa', 'lgpd'],
    applicableIndustries: ['Technology', 'Finance', 'Healthcare', 'Government'],
    fields: ['Data Protection', 'Digital Privacy', 'Cross-border Data'],
    penaltyRange: 'Up to AED 10M',
    lastUpdated: '2022-01-02'
  },
  {
    id: 'pdpb-india',
    name: 'DPDP Act',
    fullName: 'Digital Personal Data Protection Act (India)',
    description: 'Indian comprehensive data protection law for digital personal data.',
    category: 'privacy',
    regions: ['india'],
    region: 'India',
    countries: ['India'],
    icon: '🇮🇳',
    color: 'orange',
    yearIntroduced: 2023,
    maxFine: '₹500 crores',
    implementationTime: '6-12 months',
    complexity: 'High',
    keyDomains: ['digital-data', 'consent', 'data-protection', 'cross-border-transfer'],
    keyRequirements: [
      'Consent Management',
      'Data Minimization',
      'Purpose Limitation',
      'Data Security',
      'Breach Notification'
    ],
    synergies: ['gdpr', 'ccpa', 'pipeda'],
    applicableIndustries: ['Technology', 'Finance', 'Healthcare', 'E-commerce'],
    fields: ['Digital Data Protection', 'Personal Data', 'Privacy Rights'],
    penaltyRange: 'Up to ₹500 crores',
    lastUpdated: '2023-08-11'
  },
  {
    id: 'glba',
    name: 'GLBA',
    fullName: 'Gramm-Leach-Bliley Act',
    description: 'US federal law that requires financial institutions to explain information-sharing practices to customers.',
    category: 'financial',
    regions: ['us'],
    region: 'US',
    countries: ['United States'],
    icon: '🏛️',
    color: 'green',
    yearIntroduced: 1999,
    maxFine: '$100,000 per violation',
    implementationTime: '4-8 months',
    complexity: 'Medium',
    keyDomains: ['financial-privacy', 'information-sharing', 'safeguards', 'disclosure'],
    keyRequirements: [
      'Privacy Notice',
      'Opt-Out Rights',
      'Safeguards Rule',
      'Pretexting Provisions',
      'Information Sharing'
    ],
    synergies: ['sox', 'ffiec', 'pci-dss'],
    applicableIndustries: ['Banks', 'Insurance', 'Securities', 'Financial Services'],
    fields: ['Financial Privacy', 'Information Sharing', 'Consumer Protection'],
    penaltyRange: '$100,000 per violation',
    lastUpdated: '1999-11-12'
  },
  {
    id: 'ferpa',
    name: 'FERPA',
    fullName: 'Family Educational Rights and Privacy Act',
    description: 'US federal law that protects the privacy of student education records.',
    category: 'education',
    regions: ['us'],
    region: 'US',
    countries: ['United States'],
    icon: '🎓',
    color: 'blue',
    yearIntroduced: 1974,
    maxFine: 'Loss of federal funding',
    implementationTime: '2-4 months',
    complexity: 'Low',
    keyDomains: ['education-privacy', 'student-records', 'disclosure', 'access-rights'],
    keyRequirements: [
      'Student Record Privacy',
      'Parental Access Rights',
      'Disclosure Limitations',
      'Directory Information',
      'Consent Requirements'
    ],
    synergies: ['coppa', 'hipaa', 'gdpr'],
    applicableIndustries: ['Education', 'K-12 Schools', 'Universities', 'EdTech'],
    fields: ['Educational Privacy', 'Student Data', 'Academic Records'],
    penaltyRange: 'Loss of federal funding',
    lastUpdated: '2008-12-09'
  },
  {
    id: 'coppa',
    name: 'COPPA',
    fullName: 'Children\'s Online Privacy Protection Act',
    description: 'US federal law that imposes requirements on operators of websites directed to children under 13.',
    category: 'privacy',
    regions: ['us'],
    region: 'US',
    countries: ['United States'],
    icon: '👶',
    color: 'purple',
    yearIntroduced: 1998,
    maxFine: '$43,792 per violation',
    implementationTime: '2-4 months',
    complexity: 'Medium',
    keyDomains: ['children-privacy', 'parental-consent', 'data-collection', 'online-safety'],
    keyRequirements: [
      'Privacy Policy',
      'Parental Consent',
      'Limited Collection',
      'Data Security',
      'Safe Harbor'
    ],
    synergies: ['ferpa', 'ccpa', 'gdpr'],
    applicableIndustries: ['EdTech', 'Gaming', 'Social Media', 'Mobile Apps'],
    fields: ['Children\'s Privacy', 'Online Safety', 'Parental Rights'],
    penaltyRange: 'Up to $43,792 per violation',
    lastUpdated: '2013-07-01'
  },
  {
    id: 'hitech',
    name: 'HITECH',
    fullName: 'Health Information Technology for Economic and Clinical Health Act',
    description: 'US law that strengthens HIPAA privacy and security protections for health information.',
    category: 'healthcare',
    regions: ['us'],
    region: 'US',
    countries: ['United States'],
    icon: '💊',
    color: 'green',
    yearIntroduced: 2009,
    maxFine: '$1.92M per violation',
    implementationTime: '6-12 months',
    complexity: 'High',
    keyDomains: ['health-information', 'breach-notification', 'business-associates', 'audit-controls'],
    keyRequirements: [
      'Breach Notification',
      'Business Associate Agreements',
      'Audit Controls',
      'Penalty Enhancements',
      'Meaningful Use'
    ],
    synergies: ['hipaa', 'gdpr', 'iso27001'],
    applicableIndustries: ['Healthcare', 'Health Technology', 'Insurance'],
    fields: ['Health Information', 'Medical Technology', 'Healthcare Privacy'],
    penaltyRange: '$119 - $1.92M per violation',
    lastUpdated: '2013-01-25'
  },
  {
    id: 'basel-iii',
    name: 'Basel III',
    fullName: 'Basel Committee on Banking Supervision Framework',
    description: 'International regulatory framework for banks addressing risk management and capital adequacy.',
    category: 'financial',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide (Banking Jurisdictions)'],
    icon: '🏦',
    color: 'blue',
    yearIntroduced: 2010,
    maxFine: 'Regulatory sanctions, capital requirements',
    implementationTime: '12-24 months',
    complexity: 'Very High',
    keyDomains: ['capital-adequacy', 'risk-management', 'liquidity', 'leverage'],
    keyRequirements: [
      'Capital Adequacy Ratio',
      'Leverage Ratio',
      'Liquidity Coverage Ratio',
      'Net Stable Funding Ratio',
      'Risk Weighted Assets'
    ],
    synergies: ['sox', 'ffiec', 'mifid'],
    applicableIndustries: ['Banking', 'Financial Services', 'Investment Banks'],
    fields: ['Banking Regulation', 'Financial Risk', 'Capital Management'],
    penaltyRange: 'Regulatory sanctions vary by jurisdiction',
    lastUpdated: '2017-12-07'
  },
  {
    id: 'mifid-ii',
    name: 'MiFID II',
    fullName: 'Markets in Financial Instruments Directive II',
    description: 'EU legislation that regulates firms providing services to clients linked to financial instruments.',
    category: 'financial',
    regions: ['eu'],
    region: 'EU',
    countries: ['All EU Countries'],
    icon: '📈',
    color: 'indigo',
    yearIntroduced: 2018,
    maxFine: '€5M or 10% of turnover',
    implementationTime: '8-16 months',
    complexity: 'Very High',
    keyDomains: ['financial-markets', 'investor-protection', 'transparency', 'conduct'],
    keyRequirements: [
      'Transaction Reporting',
      'Best Execution',
      'Client Protection',
      'Product Governance',
      'Record Keeping'
    ],
    synergies: ['gdpr', 'basel-iii', 'sox'],
    applicableIndustries: ['Investment Services', 'Banks', 'Asset Management'],
    fields: ['Financial Markets', 'Investment Services', 'Market Regulation'],
    penaltyRange: '€5M or 10% of annual turnover',
    lastUpdated: '2018-01-03'
  },
  {
    id: 'dora',
    name: 'DORA',
    fullName: 'Digital Operational Resilience Act',
    description: 'EU regulation on digital operational resilience for the financial services sector.',
    category: 'financial',
    regions: ['eu'],
    region: 'EU',
    countries: ['All EU Countries'],
    icon: '💻',
    color: 'purple',
    yearIntroduced: 2023,
    maxFine: '€10M or 2% of turnover',
    implementationTime: '12-18 months',
    complexity: 'High',
    keyDomains: ['digital-resilience', 'ict-risk', 'incident-management', 'third-party-risk'],
    keyRequirements: [
      'ICT Risk Management',
      'Incident Reporting',
      'Digital Operational Resilience Testing',
      'Third-Party Risk Management',
      'Information Sharing'
    ],
    synergies: ['gdpr', 'nis2', 'mifid-ii'],
    applicableIndustries: ['Banks', 'Insurance', 'Investment Firms', 'Payment Services'],
    fields: ['Digital Resilience', 'ICT Risk', 'Financial Technology'],
    penaltyRange: '€10M or 2% of annual turnover',
    lastUpdated: '2023-01-17'
  },
  {
    id: 'nis2',
    name: 'NIS2',
    fullName: 'Network and Information Security Directive 2',
    description: 'EU directive on security of network and information systems across the Union.',
    category: 'security',
    regions: ['eu'],
    region: 'EU',
    countries: ['All EU Countries'],
    icon: '🌐',
    color: 'red',
    yearIntroduced: 2023,
    maxFine: '€10M or 2% of turnover',
    implementationTime: '12-18 months',
    complexity: 'High',
    keyDomains: ['cybersecurity', 'incident-response', 'supply-chain', 'essential-services'],
    keyRequirements: [
      'Cybersecurity Measures',
      'Incident Notification',
      'Supply Chain Security',
      'Vulnerability Handling',
      'Business Continuity'
    ],
    synergies: ['gdpr', 'dora', 'iso27001'],
    applicableIndustries: ['Energy', 'Transport', 'Health', 'Digital Infrastructure'],
    fields: ['Network Security', 'Critical Infrastructure', 'Cybersecurity'],
    penaltyRange: '€10M or 2% of annual turnover',
    lastUpdated: '2023-01-16'
  },
  {
    id: 'cyber-essentials',
    name: 'Cyber Essentials',
    fullName: 'UK Cyber Essentials Scheme',
    description: 'UK government-backed cybersecurity certification scheme.',
    category: 'security',
    regions: ['uk'],
    region: 'UK',
    countries: ['United Kingdom'],
    icon: '🔐',
    color: 'blue',
    yearIntroduced: 2014,
    maxFine: 'Loss of certification, contract penalties',
    implementationTime: '2-6 months',
    complexity: 'Low',
    keyDomains: ['basic-cybersecurity', 'boundary-firewalls', 'secure-configuration', 'access-control'],
    keyRequirements: [
      'Boundary Firewalls',
      'Secure Configuration',
      'Access Control',
      'Malware Protection',
      'Software Updates'
    ],
    synergies: ['iso27001', 'nist-csf', 'cis-controls'],
    applicableIndustries: ['Government Suppliers', 'SMEs', 'Technology'],
    fields: ['Basic Cybersecurity', 'Government Contracting', 'SME Security'],
    penaltyRange: 'Contract penalties, certification loss',
    lastUpdated: '2021-01-30'
  },
  {
    id: 'essential-eight',
    name: 'Essential Eight',
    fullName: 'Australian Essential Eight Cybersecurity Framework',
    description: 'Australian government cybersecurity framework with eight mitigation strategies.',
    category: 'security',
    regions: ['australia'],
    region: 'Australia',
    countries: ['Australia'],
    icon: '🇦🇺',
    color: 'orange',
    yearIntroduced: 2017,
    maxFine: 'N/A (Best practice framework)',
    implementationTime: '6-12 months',
    complexity: 'Medium',
    keyDomains: ['cybersecurity', 'malware-prevention', 'application-control', 'patching'],
    keyRequirements: [
      'Application Control',
      'Patch Applications',
      'Configure Microsoft Office',
      'User Application Hardening',
      'Restrict Administrative Privileges'
    ],
    synergies: ['nist-csf', 'iso27001', 'cis-controls'],
    applicableIndustries: ['Government', 'Critical Infrastructure', 'All Industries'],
    fields: ['Cybersecurity', 'Threat Mitigation', 'Government Security'],
    penaltyRange: 'N/A (voluntary framework)',
    lastUpdated: '2022-11-28'
  },
  {
    id: 'cmmc',
    name: 'CMMC',
    fullName: 'Cybersecurity Maturity Model Certification',
    description: 'US Department of Defense cybersecurity standard for defense contractors.',
    category: 'security',
    regions: ['us'],
    region: 'US',
    countries: ['United States'],
    icon: '🎖️',
    color: 'red',
    yearIntroduced: 2020,
    maxFine: 'Loss of DoD contracts',
    implementationTime: '12-24 months',
    complexity: 'Very High',
    keyDomains: ['defense-cybersecurity', 'controlled-information', 'supply-chain', 'third-party-assessment'],
    keyRequirements: [
      'Access Control',
      'Asset Management',
      'Audit and Accountability',
      'Configuration Management',
      'Identification and Authentication'
    ],
    synergies: ['nist-800-171', 'nist-800-53', 'iso27001'],
    applicableIndustries: ['Defense Contractors', 'Aerospace', 'Technology'],
    fields: ['Defense Security', 'Government Contracting', 'Supply Chain Security'],
    penaltyRange: 'Contract termination, business exclusion',
    lastUpdated: '2021-11-04'
  },
  {
    id: 'tisax',
    name: 'TISAX',
    fullName: 'Trusted Information Security Assessment Exchange',
    description: 'Information security assessment and exchange mechanism for the automotive industry.',
    category: 'security',
    regions: ['global'],
    region: 'Global (Automotive)',
    countries: ['Worldwide (Automotive Industry)'],
    icon: '🚗',
    color: 'gray',
    yearIntroduced: 2017,
    maxFine: 'Loss of automotive contracts',
    implementationTime: '6-12 months',
    complexity: 'High',
    keyDomains: ['automotive-security', 'information-protection', 'supplier-assessment', 'data-classification'],
    keyRequirements: [
      'Information Security',
      'Prototype Protection',
      'Data Protection',
      'Supplier Assessment',
      'Security Incident Management'
    ],
    synergies: ['iso27001', 'gdpr', 'vda-iss'],
    applicableIndustries: ['Automotive', 'Automotive Suppliers', 'Manufacturing'],
    fields: ['Automotive Security', 'Supplier Management', 'Industrial Security'],
    penaltyRange: 'Contract loss, supply chain exclusion',
    lastUpdated: '2022-06-01'
  },
  {
    id: 'pcidss-4',
    name: 'PCI DSS v4.0',
    fullName: 'Payment Card Industry Data Security Standard v4.0',
    description: 'Latest version of PCI DSS with enhanced security requirements for payment card data.',
    category: 'financial',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide'],
    icon: '💳',
    color: 'red',
    yearIntroduced: 2022,
    maxFine: '$100,000 per month',
    implementationTime: '6-12 months',
    complexity: 'High',
    keyDomains: ['payment-security', 'authentication', 'encryption', 'vulnerability-management'],
    keyRequirements: [
      'Customized Approach',
      'Authentication Testing',
      'Encryption Key Management',
      'Vulnerability Scans',
      'Network Segmentation'
    ],
    synergies: ['iso27001', 'sox', 'gdpr'],
    applicableIndustries: ['E-commerce', 'Retail', 'Financial Services', 'Hospitality'],
    fields: ['Payment Security', 'Financial Data Protection', 'E-commerce Security'],
    penaltyRange: '$5,000 - $100,000 per month',
    lastUpdated: '2022-03-31'
  },
  {
    id: 'cloud-security-alliance',
    name: 'CSA CCM',
    fullName: 'Cloud Security Alliance Cloud Controls Matrix',
    description: 'Cybersecurity control framework specifically designed for cloud computing.',
    category: 'security',
    regions: ['global'],
    region: 'Global',
    countries: ['Worldwide'],
    icon: '☁️',
    color: 'blue',
    yearIntroduced: 2010,
    maxFine: 'N/A (Best practice framework)',
    implementationTime: '4-8 months',
    complexity: 'Medium',
    keyDomains: ['cloud-security', 'data-governance', 'identity-management', 'compliance'],
    keyRequirements: [
      'Cloud Governance',
      'Data Security',
      'Identity Management',
      'Threat Management',
      'Secure Development'
    ],
    synergies: ['iso27001', 'nist-csf', 'fedramp'],
    applicableIndustries: ['Cloud Providers', 'SaaS Companies', 'Technology'],
    fields: ['Cloud Security', 'Cloud Governance', 'SaaS Security'],
    penaltyRange: 'N/A (voluntary framework)',
    lastUpdated: '2022-09-15'
  },
  {
    id: 'enisa-cybersecurity',
    name: 'ENISA Framework',
    fullName: 'European Union Agency for Cybersecurity Framework',
    description: 'EU cybersecurity framework and guidelines for member states and organizations.',
    category: 'security',
    regions: ['eu'],
    region: 'EU',
    countries: ['All EU Countries'],
    icon: '🇪🇺',
    color: 'blue',
    yearIntroduced: 2019,
    maxFine: 'Varies by member state',
    implementationTime: '6-12 months',
    complexity: 'Medium',
    keyDomains: ['eu-cybersecurity', 'incident-response', 'threat-intelligence', 'certification'],
    keyRequirements: [
      'Cybersecurity Strategy',
      'Incident Response',
      'Threat Intelligence',
      'Security Certification',
      'Public-Private Cooperation'
    ],
    synergies: ['nis2', 'gdpr', 'dora'],
    applicableIndustries: ['Critical Infrastructure', 'Government', 'Technology'],
    fields: ['EU Cybersecurity', 'Incident Response', 'Threat Intelligence'],
    penaltyRange: 'Varies by member state implementation',
    lastUpdated: '2022-11-30'
  },
  {
    id: 'c5',
    name: 'C5',
    fullName: 'Cloud Computing Compliance Controls Catalogue',
    description: 'German cloud security standard by the Federal Office for Information Security (BSI).',
    category: 'security',
    regions: ['germany'],
    region: 'Germany',
    countries: ['Germany'],
    icon: '🇩🇪',
    color: 'black',
    yearIntroduced: 2016,
    maxFine: 'N/A (Certification standard)',
    implementationTime: '6-12 months',
    complexity: 'High',
    keyDomains: ['cloud-security', 'data-protection', 'transparency', 'portability'],
    keyRequirements: [
      'Organization of Information Security',
      'Human Resources Security',
      'Asset Management',
      'Access Control',
      'Cryptography'
    ],
    synergies: ['iso27001', 'gdpr', 'bsi-standards'],
    applicableIndustries: ['Cloud Service Providers', 'Government', 'Critical Infrastructure'],
    fields: ['Cloud Security', 'German Government', 'Critical Infrastructure'],
    penaltyRange: 'N/A (voluntary certification)',
    lastUpdated: '2020-02-18'
  },
  {
    id: 'sox',
    name: 'SOX',
    fullName: 'Sarbanes-Oxley Act',
    description: 'Federal law that sets new standards for public company boards, management, and public accounting firms.',
    category: 'financial',
    regions: ['north-america'],
    region: 'North America',
    countries: ['United States'],
    icon: '📊',
    color: 'orange',
    yearIntroduced: 2002,
    maxFine: 'Up to $5M fine and 20 years prison',
    implementationTime: '6-12 months',
    complexity: 'High',
    keyDomains: ['financial-reporting', 'internal-controls', 'audit-compliance', 'corporate-governance'],
    keyRequirements: [
      'Internal Controls',
      'Financial Reporting',
      'Audit Requirements',
      'Management Assessment',
      'CEO/CFO Certification'
    ],
    synergies: ['iso27001', 'coso', 'cobit'],
    applicableIndustries: ['Public Companies', 'Finance', 'Banking'],
    fields: ['Financial Services', 'Corporate Governance', 'Public Companies'],
    penaltyRange: 'Up to $5M fine and 20 years prison',
    lastUpdated: '2002-07-30'
  },
  {
    id: 'pci-dss',
    name: 'PCI DSS',
    fullName: 'Payment Card Industry Data Security Standard',
    description: 'Security standard for organizations that handle branded credit cards from major card schemes.',
    category: 'financial',
    regions: ['global'],
    region: 'Global',
    icon: '💳',
    color: 'red',
    yearIntroduced: 2004,
    maxFine: '$100K per month for non-compliance',
    implementationTime: '3-9 months',
    complexity: 'High',
    keyDomains: ['payment-security', 'network-security', 'access-control', 'monitoring'],
    keyRequirements: [
      'Secure Network',
      'Protect Cardholder Data',
      'Vulnerability Management',
      'Access Controls',
      'Network Monitoring'
    ],
    synergies: ['iso27001', 'gdpr', 'nist'],
    applicableIndustries: ['Retail', 'E-commerce', 'Finance', 'Hospitality'],
    penaltyRange: '$5K - $100K per month',
    lastUpdated: '2022-03-31'
  },
  {
    id: 'nist',
    name: 'NIST CSF',
    fullName: 'NIST Cybersecurity Framework',
    description: 'Framework created by NIST to help organizations manage cybersecurity risks through five core functions.',
    category: 'security',
    regions: ['us', 'global'],
    region: 'US',
    icon: '🛡️',
    color: 'indigo',
    yearIntroduced: 2014,
    maxFine: 'Varies by sector',
    implementationTime: '4-10 months',
    complexity: 'Medium',
    keyDomains: ['cybersecurity', 'risk-management', 'incident-response', 'recovery'],
    keyRequirements: [
      'Identify',
      'Protect',
      'Detect',
      'Respond',
      'Recover'
    ],
    synergies: ['iso27001', 'pci-dss', 'sox'],
    applicableIndustries: ['Critical Infrastructure', 'Technology', 'Government', 'Finance'],
    penaltyRange: 'Varies by implementing regulation',
    lastUpdated: '2018-04-16'
  },
  {
    id: 'ccpa',
    name: 'CCPA',
    fullName: 'California Consumer Privacy Act',
    description: 'California privacy law that gives consumers rights regarding their personal information.',
    category: 'privacy',
    regions: ['us'],
    region: 'US',
    icon: '🌴',
    color: 'teal',
    yearIntroduced: 2020,
    maxFine: '$7,500 per violation',
    implementationTime: '3-6 months',
    complexity: 'Medium',
    keyDomains: ['consumer-privacy', 'data-rights', 'disclosure', 'opt-out'],
    keyRequirements: [
      'Consumer Rights',
      'Privacy Notices',
      'Data Mapping',
      'Opt-out Mechanisms',
      'Data Subject Requests'
    ],
    synergies: ['gdpr', 'cpra', 'pipeda'],
    applicableIndustries: ['Technology', 'Retail', 'Finance', 'Healthcare'],
    penaltyRange: '$2,500 - $7,500 per violation',
    lastUpdated: '2020-01-01'
  },
  {
    id: 'cobit',
    name: 'COBIT',
    fullName: 'Control Objectives for Information Technology',
    description: 'Framework for IT governance and management that helps ensure IT supports business objectives.',
    category: 'governance',
    regions: ['global'],
    region: 'Global',
    icon: '⚙️',
    color: 'gray',
    yearIntroduced: 1996,
    maxFine: 'N/A (Best Practice Framework)',
    implementationTime: '6-18 months',
    complexity: 'High',
    keyDomains: ['it-governance', 'risk-management', 'resource-management', 'performance-measurement'],
    keyRequirements: [
      'Governance Framework',
      'Management Processes',
      'Control Activities',
      'Information & Communication',
      'Monitoring Activities'
    ],
    synergies: ['sox', 'iso27001', 'coso'],
    applicableIndustries: ['All (IT-dependent organizations)'],
    penaltyRange: 'N/A (voluntary framework)',
    lastUpdated: '2019-02-28'
  },
  {
    id: 'nist-800-53',
    name: 'NIST 800-53',
    fullName: 'Security Controls for Federal Information Systems',
    description: 'Catalog of security and privacy controls for federal information systems and organizations.',
    category: 'security',
    regions: ['us'],
    region: 'US',
    icon: '🏛️',
    color: 'blue',
    yearIntroduced: 2005,
    maxFine: 'Contract termination, legal action',
    implementationTime: '8-16 months',
    complexity: 'Very High',
    keyDomains: ['access-control', 'audit-accountability', 'configuration-management', 'identification-authentication'],
    keyRequirements: [
      'Access Control',
      'Audit and Accountability',
      'Configuration Management',
      'Identification and Authentication',
      'System and Communications Protection'
    ],
    synergies: ['nist', 'iso27001', 'fedramp'],
    applicableIndustries: ['Government', 'Defense Contractors', 'Federal Suppliers'],
    penaltyRange: 'Contract penalties, legal consequences',
    lastUpdated: '2020-09-23'
  },
  {
    id: 'fedramp',
    name: 'FedRAMP',
    fullName: 'Federal Risk and Authorization Management Program',
    description: 'US government program that provides standardized approach to security assessment for cloud products.',
    category: 'security',
    regions: ['us'],
    region: 'US',
    icon: '☁️',
    color: 'indigo',
    yearIntroduced: 2011,
    maxFine: 'Loss of government contracts',
    implementationTime: '12-24 months',
    complexity: 'Very High',
    keyDomains: ['cloud-security', 'continuous-monitoring', 'risk-management', 'authorization'],
    keyRequirements: [
      'Security Assessment',
      'Authorization to Operate',
      'Continuous Monitoring',
      'NIST 800-53 Controls',
      'Third-Party Assessment'
    ],
    synergies: ['nist-800-53', 'iso27001', 'sox'],
    applicableIndustries: ['Cloud Service Providers', 'Government Contractors'],
    penaltyRange: 'Contract termination, business impact',
    lastUpdated: '2021-12-15'
  }
];

// Helper functions for framework data
export const getFrameworksByCategory = (category) => {
  if (category === 'all') return frameworksData;
  return frameworksData.filter(framework => framework.category === category);
};

export const getFrameworksByRegion = (region) => {
  if (region === 'all') return frameworksData;
  return frameworksData.filter(framework => 
    framework.regions.includes(region) || framework.regions.includes('global')
  );
};

export const getFrameworksByCountry = (country) => {
  if (country === 'all') return frameworksData;
  return frameworksData.filter(framework => 
    framework.countries?.some(c => c.toLowerCase().includes(country.toLowerCase())) ||
    framework.countries?.includes('Worldwide')
  );
};

export const getFrameworksByField = (field) => {
  if (field === 'all') return frameworksData;
  return frameworksData.filter(framework => 
    framework.fields?.some(f => f.toLowerCase().includes(field.toLowerCase()))
  );
};

export const searchFrameworks = (query) => {
  if (!query) return frameworksData;
  const lowercaseQuery = query.toLowerCase();
  return frameworksData.filter(framework =>
    framework.name.toLowerCase().includes(lowercaseQuery) ||
    framework.fullName.toLowerCase().includes(lowercaseQuery) ||
    framework.description.toLowerCase().includes(lowercaseQuery) ||
    framework.category.toLowerCase().includes(lowercaseQuery) ||
    framework.region.toLowerCase().includes(lowercaseQuery) ||
    framework.countries?.some(country => country.toLowerCase().includes(lowercaseQuery)) ||
    framework.fields?.some(field => field.toLowerCase().includes(lowercaseQuery))
  );
};

export const getFrameworkColor = (color) => {
  const colors = {
    blue: 'from-blue-500 to-indigo-600',
    purple: 'from-purple-500 to-violet-600',
    green: 'from-green-500 to-emerald-600',
    orange: 'from-orange-500 to-red-600',
    red: 'from-red-500 to-pink-600',
    indigo: 'from-indigo-500 to-purple-600',
    teal: 'from-teal-500 to-cyan-600',
    gray: 'from-gray-500 to-slate-600',
    black: 'from-gray-800 to-black'
  };
  return colors[color] || colors.blue;
};

// Get unique values for filtering
export const getUniqueCategories = () => {
  return [...new Set(frameworksData.map(f => f.category))].sort();
};

export const getUniqueRegions = () => {
  const allRegions = frameworksData.flatMap(f => f.regions);
  return [...new Set(allRegions)].sort();
};

export const getUniqueCountries = () => {
  const allCountries = frameworksData.flatMap(f => f.countries || []);
  return [...new Set(allCountries)].filter(c => c !== 'Worldwide').sort();
};

export const getUniqueFields = () => {
  const allFields = frameworksData.flatMap(f => f.fields || []);
  return [...new Set(allFields)].sort();
};

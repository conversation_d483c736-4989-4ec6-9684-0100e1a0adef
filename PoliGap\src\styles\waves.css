@keyframes gradientWave {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-wave-bg {
  background: linear-gradient(-45deg, #f0f9ff, #e6f2ff, #e0f2fe, #dbeafe);
  background-size: 400% 400%;
  animation: gradientWave 8s ease infinite;
}

.gradient-wave-bg-fast {
  background: linear-gradient(-45deg, #f0f9ff, #e6f2ff, #e0f2fe, #dbeafe);
  background-size: 400% 400%;
  animation: gradientWave 5s ease infinite;
}

/* Extra wavy elements for decorative purposes */
.wavy-decoration {
  position: absolute;
  width: 100%;
  height: 100px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23dbeafe' fill-opacity='0.5' d='M0,96L60,106.7C120,117,240,139,360,133.3C480,128,600,96,720,90.7C840,85,960,107,1080,117.3C1200,128,1320,128,1380,128L1440,128L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  background-repeat: no-repeat;
  z-index: -1;
  animation: waveMove 10s ease-in-out infinite alternate;
}

@keyframes waveMove {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(10px);
  }
}
